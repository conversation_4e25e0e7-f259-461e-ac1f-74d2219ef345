import streamlit as st
from utils import extract_text, generate_summary, ask_question
import openai
from dotenv import load_dotenv
import time

load_dotenv()

# Enhanced page configuration
st.set_page_config(
    page_title="📄 AI Document Chatbot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for modern UI
st.markdown("""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* Global Styles */
    .main {
        font-family: 'Inter', sans-serif;
    }

    /* Header Styling */
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .main-header h1 {
        color: white;
        font-size: 3rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .main-header p {
        color: rgba(255,255,255,0.9);
        font-size: 1.2rem;
        margin: 0.5rem 0 0 0;
        font-weight: 300;
    }

    /* Upload Section */
    .upload-section {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        border: 1px solid #e1e5e9;
        margin-bottom: 2rem;
    }

    /* Success Message */
    .success-message {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        margin: 1rem 0;
        font-weight: 500;
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
    }

    /* Action Cards */
    .action-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        border: 1px solid #e1e5e9;
        margin: 1rem 0;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .action-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }

    /* Question Section */
    .question-section {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        padding: 2rem;
        border-radius: 15px;
        margin: 2rem 0;
    }

    .question-section h3 {
        color: white;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    /* Answer Box */
    .answer-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        margin: 1rem 0;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    }

    .answer-box h4 {
        margin-bottom: 1rem;
        font-weight: 600;
        color: #fff;
    }

    /* Summary Box */
    .summary-box {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        margin: 1rem 0;
        box-shadow: 0 6px 20px rgba(17, 153, 142, 0.3);
    }

    .summary-box h4 {
        margin-bottom: 1rem;
        font-weight: 600;
        color: #fff;
    }

    /* Button Styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    /* File Uploader Styling */
    .uploadedFile {
        border: 2px dashed #667eea;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        background: rgba(102, 126, 234, 0.05);
    }

    /* Sidebar Styling */
    .css-1d391kg {
        background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    }

    /* Hide Streamlit Branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Responsive Design */
    @media (max-width: 768px) {
        .main-header h1 {
            font-size: 2rem;
        }
        .main-header p {
            font-size: 1rem;
        }
    }
</style>
""", unsafe_allow_html=True)

# Main Header
st.markdown("""
<div class="main-header">
    <h1>🤖 AI Document Chatbot</h1>
    <p>Upload your documents and chat with AI to get insights, summaries, and answers</p>
</div>
""", unsafe_allow_html=True)

# Sidebar with information
with st.sidebar:
    st.markdown("### 📋 How to Use")
    st.markdown("""
    1. **Upload** a PDF, TXT, or DOCX file
    2. **Generate** a summary of your document
    3. **Ask** questions about the content
    4. **Get** AI-powered answers instantly
    """)

    st.markdown("### 📊 Supported Formats")
    st.markdown("• PDF files")
    st.markdown("• Text files (.txt)")
    st.markdown("• Word documents (.docx)")

    st.markdown("### 🔧 Features")
    st.markdown("• AI-powered document analysis")
    st.markdown("• Intelligent summarization")
    st.markdown("• Interactive Q&A")
    st.markdown("• Multi-format support")

# Main content area
col1, col2 = st.columns([2, 1])

with col1:
    # Upload section
    st.markdown('<div class="upload-section">', unsafe_allow_html=True)
    st.markdown("### 📁 Upload Your Document")
    uploaded_file = st.file_uploader(
        "Choose a file to analyze",
        type=["pdf", "txt", "docx"],
        help="Upload a PDF, TXT, or DOCX file to get started"
    )
    st.markdown('</div>', unsafe_allow_html=True)

with col2:
    if uploaded_file:
        st.markdown("### 📄 File Info")
        st.info(f"**Filename:** {uploaded_file.name}")
        st.info(f"**Size:** {uploaded_file.size:,} bytes")
        st.info(f"**Type:** {uploaded_file.type}")

if uploaded_file:
    # Processing indicator
    with st.spinner("📄 Processing your document..."):
        doc_text = extract_text(uploaded_file)
        time.sleep(0.5)  # Small delay for better UX

    # Success message
    st.markdown("""
    <div class="success-message">
        ✅ Document uploaded and processed successfully! You can now generate a summary or ask questions.
    </div>
    """, unsafe_allow_html=True)

    # Action buttons in columns
    col1, col2 = st.columns(2)

    with col1:
        st.markdown('<div class="action-card">', unsafe_allow_html=True)
        st.markdown("#### 📝 Document Summary")
        st.markdown("Get an AI-generated summary of your document's key points and main ideas.")

        if st.button("🧠 Generate Summary", key="summary_btn"):
            with st.spinner("� Analyzing document and generating summary..."):
                summary = generate_summary(doc_text)

            st.markdown(f"""
            <div class="summary-box">
                <h4>📝 Document Summary</h4>
                <p>{summary}</p>
            </div>
            """, unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)

    with col2:
        st.markdown('<div class="action-card">', unsafe_allow_html=True)
        st.markdown("#### 💬 Ask Questions")
        st.markdown("Ask specific questions about your document and get detailed AI-powered answers.")
        st.markdown('</div>', unsafe_allow_html=True)

    # Question section
    st.markdown("""
    <div class="question-section">
        <h3>💬 Ask a Question About Your Document</h3>
    </div>
    """, unsafe_allow_html=True)

    question = st.text_input(
        "What would you like to know?",
        placeholder="e.g., What are the main conclusions? Who are the key people mentioned?",
        help="Type your question about the document content"
    )

    if question:
        with st.spinner("🤖 Thinking and analyzing..."):
            answer = ask_question(doc_text, question)
            time.sleep(0.3)  # Small delay for better UX

        st.markdown(f"""
        <div class="answer-box">
            <h4>🤖 AI Answer</h4>
            <p><strong>Question:</strong> {question}</p>
            <p><strong>Answer:</strong> {answer}</p>
        </div>
        """, unsafe_allow_html=True)

else:
    # Welcome message when no file is uploaded
    st.markdown("""
    <div class="action-card" style="text-align: center; padding: 3rem;">
        <h3>👋 Welcome to AI Document Chatbot!</h3>
        <p style="font-size: 1.1rem; color: #666; margin: 1rem 0;">
            Upload a document to get started with AI-powered analysis, summaries, and Q&A.
        </p>
        <p style="color: #888;">
            Supported formats: PDF, TXT, DOCX
        </p>
    </div>
    """, unsafe_allow_html=True)
