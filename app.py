import streamlit as st
from utils import extract_text, generate_summary, ask_question
import openai
from dotenv import load_dotenv

load_dotenv()


st.set_page_config(page_title="📄 Document Chatbot", layout="wide")

st.title("📄 AI Document Chatbot")

uploaded_file = st.file_uploader("Upload a PDF, TXT, or DOCX file", type=["pdf", "txt", "docx"])

if uploaded_file:
    with st.spinner("📄 Reading the document..."):
        doc_text = extract_text(uploaded_file)

    st.success("✅ Document uploaded and read successfully!")

    if st.button("Generate Summary"):
        with st.spinner("🧠 Generating summary..."):
            summary = generate_summary(doc_text)
            st.subheader("📝 Summary")
            st.write(summary)

    st.markdown("---")
    st.subheader("💬 Ask a question about the document")

    question = st.text_input("Type your question here")

    if question:
        with st.spinner("🤖 Thinking..."):
            answer = ask_question(doc_text, question)
            st.markdown("**Answer:**")
            st.write(answer)
