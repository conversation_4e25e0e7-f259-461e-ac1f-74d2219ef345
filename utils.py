import os
import PyPDF2
import docx
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))  # ✅ uses friend's key from .env

def extract_text(file):
    if file.name.endswith('.pdf'):
        reader = PyPDF2.PdfReader(file)
        return "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])
    elif file.name.endswith('.txt'):
        return file.read().decode("utf-8")
    elif file.name.endswith('.docx'):
        doc = docx.Document(file)
        return "\n".join([para.text for para in doc.paragraphs])
    return ""

def generate_summary(text):
    prompt = f"Summarize the following document:\n\n{text}"
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",  # or "gpt-4" if your friend’s key supports it
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content

def ask_question(text, question):
    prompt = f"""Here's the document:
{text}

Now answer this question based on it:
{question}
"""
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",  # or "gpt-4"
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content
